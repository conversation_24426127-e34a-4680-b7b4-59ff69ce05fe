globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/calculate/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js":{"*":{"id":"(ssr)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/material/CssBaseline/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/material/CssBaseline/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/styled-engine/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/styled-engine/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Box/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Box/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Container/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Container/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/createBox.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/createBox.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/GlobalStyles/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/GlobalStyles/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Stack/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/Stack.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Stack/Stack.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/ThemeProvider/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/ThemeProvider/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Unstable_Grid/Grid.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Unstable_Grid/Grid.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/Unstable_Grid/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/Unstable_Grid/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useTheme.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useTheme.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeProps/index.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useThemeProps/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeWithoutDefault.js":{"*":{"id":"(ssr)/./node_modules/@mui/system/esm/useThemeWithoutDefault.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useControlled/useControlled.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useControlled/useControlled.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useId/useId.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useId/useId.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js":{"*":{"id":"(ssr)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/Provider.tsx":{"*":{"id":"(ssr)/./src/app/Provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/providers/ToastProvider.tsx":{"*":{"id":"(ssr)/./src/providers/ToastProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/utils/theme.ts":{"*":{"id":"(ssr)/./src/utils/theme.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/calculate/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/calculate/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/calculate/layout.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/calculate/layout.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\material-nextjs\\v13-appRouter\\appRouterV13.js":{"id":"(app-pages-browser)/./node_modules/@mui/material-nextjs/v13-appRouter/appRouterV13.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\material\\CssBaseline\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/material/CssBaseline/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\styled-engine\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/styled-engine/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\Box\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Box/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\Container\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Container/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\createBox.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/createBox.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\cssVars\\useCurrentColorScheme.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/cssVars/useCurrentColorScheme.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\GlobalStyles\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/GlobalStyles/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\Stack\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\Stack\\Stack.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Stack/Stack.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\ThemeProvider\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/ThemeProvider/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\Unstable_Grid\\Grid.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Unstable_Grid/Grid.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\Unstable_Grid\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/Unstable_Grid/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\useMediaQuery\\useMediaQuery.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\useTheme.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useTheme.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\useThemeProps\\index.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeProps/index.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\system\\esm\\useThemeWithoutDefault.js":{"id":"(app-pages-browser)/./node_modules/@mui/system/esm/useThemeWithoutDefault.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useControlled\\useControlled.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useControlled/useControlled.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useEnhancedEffect\\useEnhancedEffect.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useEventCallback\\useEventCallback.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useEventCallback/useEventCallback.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useForkRef\\useForkRef.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useForkRef/useForkRef.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useId\\useId.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useId/useId.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useIsFocusVisible\\useIsFocusVisible.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useLazyRef\\useLazyRef.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useLazyRef/useLazyRef.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useOnMount\\useOnMount.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useOnMount/useOnMount.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\usePreviousProps\\usePreviousProps.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useSlotProps\\useSlotProps.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useSlotProps/useSlotProps.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\@mui\\utils\\esm\\useTimeout\\useTimeout.js":{"id":"(app-pages-browser)/./node_modules/@mui/utils/esm/useTimeout/useTimeout.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\utils\\\\Font.ts\",\"import\":\"Montserrat\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\"}],\"variableName\":\"FontMontserrat\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\utils\\\\Font.ts\",\"import\":\"Montserrat\",\"arguments\":[{\"weight\":[\"400\",\"500\",\"700\"],\"subsets\":[\"latin\"],\"variable\":\"--font-montserrat\"}],\"variableName\":\"FontMontserrat\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\src\\app\\Provider.tsx":{"id":"(app-pages-browser)/./src/app/Provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\src\\providers\\ToastProvider.tsx":{"id":"(app-pages-browser)/./src/providers/ToastProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\src\\utils\\theme.ts":{"id":"(app-pages-browser)/./src/utils/theme.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\src\\app\\dashboard\\layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/layout.tsx","name":"*","chunks":["app/dashboard/layout","static/chunks/app/dashboard/layout.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\src\\app\\dashboard\\calculate\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/calculate/page.tsx","name":"*","chunks":["app/dashboard/calculate/page","static/chunks/app/dashboard/calculate/page.js"],"async":false},"E:\\salaam-project\\salam-store-fe-main\\src\\app\\dashboard\\calculate\\layout.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/calculate/layout.tsx","name":"*","chunks":["app/dashboard/calculate/layout","static/chunks/app/dashboard/calculate/layout.js"],"async":false}},"entryCSSFiles":{"E:\\salaam-project\\salam-store-fe-main\\src\\app\\(auth)\\layout":[],"E:\\salaam-project\\salam-store-fe-main\\src\\app\\layout":["static/css/app/layout.css"],"E:\\salaam-project\\salam-store-fe-main\\src\\app\\not-found":[],"E:\\salaam-project\\salam-store-fe-main\\src\\app\\dashboard\\layout":[],"E:\\salaam-project\\salam-store-fe-main\\src\\app\\dashboard\\calculate\\page":[],"E:\\salaam-project\\salam-store-fe-main\\src\\app\\dashboard\\calculate\\layout":[]}}